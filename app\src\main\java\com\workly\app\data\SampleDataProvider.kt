package com.workly.app.data

import com.workly.app.data.model.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

object SampleDataProvider {

    fun getSampleShifts(): List<Shift> {
        return listOf(
            Shift(
                id = "shift_1",
                name = "<PERSON>a hành chính",
                startTime = "08:00",
                endTime = "17:00",
                departureTime = "07:30",
                maxEndTime = "17:30",
                daysApplied = listOf("Mon", "<PERSON>e", "Wed", "Thu", "Fri"),
                remindBeforeStart = 30,
                remindAfterEnd = 15,
                showPunch = true,
                breakMinutes = 60,
                isNightShift = false,
                createdAt = LocalDateTime.now().minusDays(30),
                updatedAt = LocalDateTime.now().minusDays(1)
            ),
            Shift(
                id = "shift_2",
                name = "Ca tối",
                startTime = "18:00",
                endTime = "02:00",
                departureTime = "17:30",
                maxEndTime = "02:30",
                daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
                remindBeforeStart = 45,
                remindAfterEnd = 20,
                showPunch = true,
                breakMinutes = 30,
                isNightShift = true,
                createdAt = LocalDateTime.now().minusDays(25),
                updatedAt = LocalDateTime.now().minusDays(2)
            ),
            Shift(
                id = "shift_3",
                name = "Ca cuối tuần",
                startTime = "09:00",
                endTime = "15:00",
                departureTime = "08:30",
                maxEndTime = "15:30",
                daysApplied = listOf("Sat", "Sun"),
                remindBeforeStart = 60,
                remindAfterEnd = 10,
                showPunch = false,
                breakMinutes = 45,
                isNightShift = false,
                createdAt = LocalDateTime.now().minusDays(20),
                updatedAt = LocalDateTime.now().minusDays(3)
            )
        )
    }

    fun getSampleAttendanceLogs(): List<AttendanceLog> {
        val now = LocalDateTime.now()
        return listOf(
            AttendanceLog(
                id = "log_1",
                type = AttendanceType.GO_WORK,
                time = now.minusHours(2),
                shiftId = "shift_1",
                location = "Nhà riêng - 123 Đường ABC, Hà Nội",
                isAutoGenerated = false,
                note = "Đi làm đúng giờ"
            ),
            AttendanceLog(
                id = "log_2",
                type = AttendanceType.CHECK_IN,
                time = now.minusHours(1).minusMinutes(30),
                shiftId = "shift_1",
                location = "Văn phòng công ty - 456 Đường XYZ, Hà Nội",
                isAutoGenerated = true,
                note = null
            ),
            AttendanceLog(
                id = "log_3",
                type = AttendanceType.CHECK_OUT,
                time = now.minusDays(1).withHour(17).withMinute(15),
                shiftId = "shift_1",
                location = "Văn phòng công ty - 456 Đường XYZ, Hà Nội",
                isAutoGenerated = false,
                note = "Hoàn thành công việc"
            ),
            AttendanceLog(
                id = "log_4",
                type = AttendanceType.COMPLETE,
                time = now.minusDays(1).withHour(18).withMinute(0),
                shiftId = "shift_1",
                location = "Nhà riêng - 123 Đường ABC, Hà Nội",
                isAutoGenerated = true,
                note = null
            ),
            AttendanceLog(
                id = "log_5",
                type = AttendanceType.GO_WORK,
                time = now.minusDays(2).withHour(7).withMinute(45),
                shiftId = "shift_1",
                location = "Nhà riêng - 123 Đường ABC, Hà Nội",
                isAutoGenerated = false,
                note = "Đi sớm 15 phút"
            )
        )
    }

    fun getSampleNotes(): List<Note> {
        val now = LocalDateTime.now()
        return listOf(
            Note(
                id = "note_1",
                title = "Họp team hàng tuần",
                content = "Họp team vào thứ 2 hàng tuần lúc 9:00 AM để review công việc tuần trước và lập kế hoạch tuần mới.",
                isPriority = true,
                reminderDateTime = now.plusDays(1).withHour(8).withMinute(30),
                associatedShiftIds = listOf("shift_1"),
                createdAt = now.minusDays(7),
                updatedAt = now.minusDays(1),
                isHiddenFromHome = false,
                snoozeUntil = null,
                enableNotifications = true
            ),
            Note(
                id = "note_2",
                title = "Nộp báo cáo tháng",
                content = "Cần hoàn thành và nộp báo cáo công việc tháng này trước ngày 30. Bao gồm: tổng kết KPI, đánh giá hiệu suất, kế hoạch tháng sau.",
                isPriority = true,
                reminderDateTime = now.plusDays(3).withHour(14).withMinute(0),
                associatedShiftIds = listOf("shift_1"),
                createdAt = now.minusDays(5),
                updatedAt = now.minusDays(2),
                isHiddenFromHome = false,
                snoozeUntil = null,
                enableNotifications = true
            ),
            Note(
                id = "note_3",
                title = "Kiểm tra sức khỏe định kỳ",
                content = "Đặt lịch khám sức khỏe định kỳ 6 tháng tại bệnh viện. Cần mang theo sổ khám bệnh và thẻ BHYT.",
                isPriority = false,
                reminderDateTime = now.plusDays(7).withHour(10).withMinute(0),
                associatedShiftIds = emptyList(),
                createdAt = now.minusDays(3),
                updatedAt = now.minusDays(1),
                isHiddenFromHome = false,
                snoozeUntil = null,
                enableNotifications = true
            ),
            Note(
                id = "note_4",
                title = "Cập nhật CV và LinkedIn",
                content = "Cập nhật thông tin mới nhất về kinh nghiệm làm việc, kỹ năng và chứng chỉ vào CV và profile LinkedIn.",
                isPriority = false,
                reminderDateTime = null,
                associatedShiftIds = emptyList(),
                createdAt = now.minusDays(10),
                updatedAt = now.minusDays(8),
                isHiddenFromHome = false,
                snoozeUntil = null,
                enableNotifications = false
            ),
            Note(
                id = "note_5",
                title = "Học khóa học online",
                content = "Tiếp tục học khóa học Android Development trên Udemy. Hiện tại đã hoàn thành 60%, cần hoàn thành trong 2 tuần tới.",
                isPriority = false,
                reminderDateTime = now.plusDays(2).withHour(20).withMinute(0),
                associatedShiftIds = emptyList(),
                createdAt = now.minusDays(15),
                updatedAt = now.minusDays(5),
                isHiddenFromHome = false,
                snoozeUntil = null,
                enableNotifications = true
            )
        )
    }

    fun getSampleDailyWorkStatus(): List<DailyWorkStatus> {
        val today = LocalDate.now()
        return listOf(
            DailyWorkStatus(
                date = today,
                status = WorkStatus.DA_DI_CHUA_VAO,
                appliedShiftIdForDay = "shift_1",
                vaoLogTime = null,
                raLogTime = null,
                standardHoursScheduled = 8.0,
                otHoursScheduled = 0.0,
                sundayHoursScheduled = 0.0,
                nightHoursScheduled = 0.0,
                totalHoursScheduled = 8.0,
                lateMinutes = 0,
                earlyMinutes = 0,
                isHolidayWork = false,
                isManualOverride = false
            ),
            DailyWorkStatus(
                date = today.minusDays(1),
                status = WorkStatus.DU_CONG,
                appliedShiftIdForDay = "shift_1",
                vaoLogTime = today.minusDays(1).atTime(8, 0),
                raLogTime = today.minusDays(1).atTime(17, 15),
                standardHoursScheduled = 8.0,
                otHoursScheduled = 0.25,
                sundayHoursScheduled = 0.0,
                nightHoursScheduled = 0.0,
                totalHoursScheduled = 8.25,
                lateMinutes = 0,
                earlyMinutes = 0,
                isHolidayWork = false,
                isManualOverride = false
            ),
            DailyWorkStatus(
                date = today.minusDays(2),
                status = WorkStatus.DI_MUON,
                appliedShiftIdForDay = "shift_1",
                vaoLogTime = today.minusDays(2).atTime(8, 15),
                raLogTime = today.minusDays(2).atTime(17, 0),
                standardHoursScheduled = 8.0,
                otHoursScheduled = 0.0,
                sundayHoursScheduled = 0.0,
                nightHoursScheduled = 0.0,
                totalHoursScheduled = 7.75,
                lateMinutes = 15,
                earlyMinutes = 0,
                isHolidayWork = false,
                isManualOverride = false
            ),
            DailyWorkStatus(
                date = today.minusDays(3),
                status = WorkStatus.VE_SOM,
                appliedShiftIdForDay = "shift_1",
                vaoLogTime = today.minusDays(3).atTime(8, 0),
                raLogTime = today.minusDays(3).atTime(16, 30),
                standardHoursScheduled = 8.0,
                otHoursScheduled = 0.0,
                sundayHoursScheduled = 0.0,
                nightHoursScheduled = 0.0,
                totalHoursScheduled = 7.5,
                lateMinutes = 0,
                earlyMinutes = 30,
                isHolidayWork = false,
                isManualOverride = false
            ),
            DailyWorkStatus(
                date = today.minusDays(4),
                status = WorkStatus.NGHI_PHEP,
                appliedShiftIdForDay = null,
                vaoLogTime = null,
                raLogTime = null,
                standardHoursScheduled = 0.0,
                otHoursScheduled = 0.0,
                sundayHoursScheduled = 0.0,
                nightHoursScheduled = 0.0,
                totalHoursScheduled = 0.0,
                lateMinutes = 0,
                earlyMinutes = 0,
                isHolidayWork = false,
                isManualOverride = true
            )
        )
    }

    fun getSampleUserSettings(): UserSettings {
        return UserSettings(
            language = "vi",
            theme = Theme.LIGHT,
            multiButtonMode = MultiButtonMode.AUTO,
            alarmSoundEnabled = true,
            alarmVibrationEnabled = true,
            weatherWarningEnabled = true,
            weatherLocation = WeatherLocation(
                home = LocationCoordinate(21.0285, 105.8542), // Hà Nội
                work = LocationCoordinate(21.0245, 105.8412), // Quận Ba Đình
                useSingleLocation = false
            ),
            changeShiftReminderMode = ChangeShiftReminderMode.ASK_WEEKLY,
            timeFormat = TimeFormat.TWENTY_FOUR_HOUR,
            firstDayOfWeek = FirstDayOfWeek.MONDAY,
            homeLocation = SavedLocation(
                id = "home_1",
                name = "Nhà riêng",
                address = "123 Đường ABC, Phường XYZ, Quận Cầu Giấy, Hà Nội",
                latitude = 21.0285,
                longitude = 105.8542,
                radius = 100,
                createdAt = LocalDateTime.now().minusDays(30).toString(),
                updatedAt = LocalDateTime.now().minusDays(1).toString()
            ),
            workLocation = SavedLocation(
                id = "work_1",
                name = "Văn phòng công ty",
                address = "456 Đường XYZ, Phường ABC, Quận Ba Đình, Hà Nội",
                latitude = 21.0245,
                longitude = 105.8412,
                radius = 50,
                createdAt = LocalDateTime.now().minusDays(30).toString(),
                updatedAt = LocalDateTime.now().minusDays(1).toString()
            ),
            autoCheckInEnabled = true,
            autoCheckInRadius = 100,
            locationTrackingEnabled = true,
            lateThresholdMinutes = 15,
            rapidPressThresholdSeconds = 30,
            overtimeRates = OvertimeRates(
                weekday = 1.5,
                saturday = 2.0,
                sunday = 2.0,
                holiday = 3.0
            ),
            notesDisplayCount = 3,
            notesTimeWindow = NotesTimeWindow.ALWAYS,
            notesShowConflictWarning = true,
            rotationConfig = null
        )
    }

    fun getSampleWeatherData(): WeatherData {
        val now = LocalDateTime.now()
        return WeatherData(
            current = CurrentWeather(
                temperature = 28.5,
                description = "Nắng ít mây",
                icon = "01d",
                location = "Hà Nội"
            ),
            forecast = listOf(
                WeatherForecast(
                    time = now.plusHours(3),
                    temperature = 30.0,
                    description = "Nắng",
                    icon = "01d"
                ),
                WeatherForecast(
                    time = now.plusHours(6),
                    temperature = 32.0,
                    description = "Nắng nóng",
                    icon = "01d"
                ),
                WeatherForecast(
                    time = now.plusHours(9),
                    temperature = 29.0,
                    description = "Có mây",
                    icon = "02d"
                ),
                WeatherForecast(
                    time = now.plusHours(12),
                    temperature = 26.0,
                    description = "Mưa nhẹ",
                    icon = "10n"
                )
            ),
            warnings = listOf(
                WeatherWarning(
                    type = WeatherWarningType.HEAT,
                    message = "Nhiệt độ cao 32°C vào buổi chiều, hãy mang theo nước và tránh nắng",
                    location = WeatherWarningLocation.WORK,
                    time = now.plusHours(6)
                ),
                WeatherWarning(
                    type = WeatherWarningType.RAIN,
                    message = "Có mưa vào buổi tối, hãy mang theo ô",
                    location = WeatherWarningLocation.HOME,
                    time = now.plusHours(12)
                )
            ),
            lastUpdated = now
        )
    }
}
