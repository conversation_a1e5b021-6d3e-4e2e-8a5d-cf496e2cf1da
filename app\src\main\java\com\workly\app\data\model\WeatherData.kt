package com.workly.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
data class WeatherData(
    val current: CurrentWeather,
    val forecast: List<WeatherForecast>,
    val warnings: List<WeatherWarning> = emptyList(),
    val lastUpdated: LocalDateTime
) : Parcelable

@Parcelize
data class CurrentWeather(
    val temperature: Double,
    val description: String,
    val icon: String,
    val location: String
) : Parcelable

@Parcelize
data class WeatherForecast(
    val time: LocalDateTime,
    val temperature: Double,
    val description: String,
    val icon: String
) : Parcelable

@Parcelize
data class WeatherWarning(
    val type: WeatherWarningType,
    val message: String,
    val location: WeatherWarningLocation,
    val time: LocalDateTime
) : Parcelable

enum class WeatherWarningType {
    RAIN,           // Mưa
    HEAT,           // Nắng nóng
    COLD,           // Lạnh
    STORM,          // Bão
    SNOW,           // Tuyết
    EXTREME_HEAT,   // Nắng nóng cực đoan
    EXTREME_COLD,   // Lạnh giá
    HIGH_HUMIDITY,  // Độ ẩm cao
    STRONG_WIND,    // <PERSON><PERSON><PERSON> mạnh
    FOG,            // Sương mù
    HAIL            // Mưa đá
}

enum class WeatherWarningLocation {
    HOME, WORK
}

// Extension functions
fun WeatherWarningType.getDisplayText(): String {
    return when (this) {
        WeatherWarningType.RAIN -> "Mưa"
        WeatherWarningType.HEAT -> "Nắng nóng"
        WeatherWarningType.COLD -> "Lạnh"
        WeatherWarningType.STORM -> "Bão"
        WeatherWarningType.SNOW -> "Tuyết"
        WeatherWarningType.EXTREME_HEAT -> "Nắng nóng cực đoan"
        WeatherWarningType.EXTREME_COLD -> "Lạnh giá"
        WeatherWarningType.HIGH_HUMIDITY -> "Độ ẩm cao"
        WeatherWarningType.STRONG_WIND -> "Gió mạnh"
        WeatherWarningType.FOG -> "Sương mù"
        WeatherWarningType.HAIL -> "Mưa đá"
    }
}

fun WeatherWarningLocation.getDisplayText(): String {
    return when (this) {
        WeatherWarningLocation.HOME -> "Nhà"
        WeatherWarningLocation.WORK -> "Công ty"
    }
}

fun CurrentWeather.getTemperatureDisplay(): String {
    return "${temperature.toInt()}°C"
}
