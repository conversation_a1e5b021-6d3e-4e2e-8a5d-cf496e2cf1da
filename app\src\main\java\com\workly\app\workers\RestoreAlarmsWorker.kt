package com.workly.app.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.workly.app.data.repository.NoteRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.services.NotificationService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first
import java.time.LocalDateTime
import java.time.ZoneId

@HiltWorker
class RestoreAlarmsWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val shiftRepository: ShiftRepository,
    private val noteRepository: NoteRepository,
    private val notificationService: NotificationService
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            restoreShiftAlarms()
            restoreNoteAlarms()
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }

    private suspend fun restoreShiftAlarms() {
        val shifts = shiftRepository.getAllShifts().first()
        val now = LocalDateTime.now()

        shifts.forEach { shift ->
            // Calculate next reminder time for this shift
            // This is a simplified version - you'd need more complex logic
            // to determine the next occurrence of the shift
            val nextReminderTime = calculateNextShiftReminderTime(shift, now)
            
            if (nextReminderTime != null) {
                val reminderTimeMillis = nextReminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                notificationService.scheduleShiftReminder(shift, reminderTimeMillis)
            }
        }
    }

    private suspend fun restoreNoteAlarms() {
        val notes = noteRepository.getNotesWithReminders().first()
        val now = LocalDateTime.now()

        for (note in notes) {
            note.reminderDateTime?.let { reminderTime ->
                if (reminderTime.isAfter(now)) {
                    notificationService.scheduleNoteReminder(note)
                }
            }
        }
    }

    private fun calculateNextShiftReminderTime(shift: com.workly.app.data.model.Shift, now: LocalDateTime): LocalDateTime? {
        // This is a placeholder implementation
        // You would need to implement proper logic to calculate the next occurrence
        // of the shift based on its schedule and reminder settings
        return null
    }
}
