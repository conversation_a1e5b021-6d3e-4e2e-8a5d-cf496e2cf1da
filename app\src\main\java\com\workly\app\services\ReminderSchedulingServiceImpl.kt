package com.workly.app.services

import android.content.Context
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import com.workly.app.data.repository.NoteRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReminderSchedulingServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val notificationService: NotificationService,
    private val shiftRepository: ShiftRepository,
    private val noteRepository: NoteRepository,
    private val settingsRepository: SettingsRepository
) : ReminderSchedulingService {

    private val schedulingStrategy: ReminderSchedulingStrategy = JustInTimeSchedulingStrategy()

    private val scheduledReminders = mutableMapOf<String, ScheduledReminder>()
    
    override suspend fun syncNextReminders() {
        try {
            // 1. Dọn dẹp tất cả nhắc nhở cũ
            clearAllShiftReminders()
            
            // 2. Lấy context hiện tại
            val context = buildReminderContext()
            
            // 3. Tính toán nhắc nhở tiếp theo
            val nextReminders = schedulingStrategy.calculateNextReminders(context)
            
            // 4. Lên lịch các nhắc nhở mới
            nextReminders.forEach { reminder ->
                scheduleReminder(reminder)
            }
            
        } catch (e: Exception) {
            // Log error
        }
    }
    
    override suspend fun clearAllShiftReminders() {
        // Hủy tất cả nhắc nhở ca đã lên lịch với ID pattern
        val shiftReminderIds = scheduledReminders.keys.filter { 
            it.startsWith("departure-") || 
            it.startsWith("checkin-") || 
            it.startsWith("checkout-") 
        }
        
        shiftReminderIds.forEach { id ->
            notificationService.cancelShiftReminder(id)
            scheduledReminders.remove(id)
        }
    }
    
    override suspend fun scheduleNextShiftReminders(shift: Shift) {
        val now = LocalDateTime.now()
        
        // Tìm và lên lịch nhắc nhở departure tiếp theo
        findNextReminderTime(shift, ShiftReminderType.DEPARTURE, now)?.let { time ->
            val reminder = ScheduledReminder(
                id = "departure-${time.toLocalDate()}",
                type = ReminderType.SHIFT_DEPARTURE,
                title = "Chuẩn bị đi làm",
                message = "Ca ${shift.name} sắp bắt đầu lúc ${shift.startTime}",
                scheduledTime = time,
                status = ReminderStatus.SCHEDULED,
                relatedId = shift.id
            )
            scheduleReminder(reminder)
        }
        
        // Tìm và lên lịch nhắc nhở check-in tiếp theo
        findNextReminderTime(shift, ShiftReminderType.CHECK_IN, now)?.let { time ->
            val reminder = ScheduledReminder(
                id = "checkin-${time.toLocalDate()}",
                type = ReminderType.SHIFT_CHECK_IN,
                title = "Nhắc vào làm",
                message = "Đã đến giờ vào làm ca ${shift.name}",
                scheduledTime = time,
                status = ReminderStatus.SCHEDULED,
                relatedId = shift.id
            )
            scheduleReminder(reminder)
        }
        
        // Tìm và lên lịch nhắc nhở check-out tiếp theo
        findNextReminderTime(shift, ShiftReminderType.CHECK_OUT, now)?.let { time ->
            val reminder = ScheduledReminder(
                id = "checkout-${time.toLocalDate()}",
                type = ReminderType.SHIFT_CHECK_OUT,
                title = "Nhắc ra về",
                message = "Đã đến giờ kết thúc ca ${shift.name}",
                scheduledTime = time,
                status = ReminderStatus.SCHEDULED,
                relatedId = shift.id
            )
            scheduleReminder(reminder)
        }
    }
    
    override suspend fun scheduleNextNoteReminders() {
        val now = LocalDateTime.now()
        val notes = noteRepository.getActiveNotesFlow().first()
        
        // Tìm ghi chú có nhắc nhở sớm nhất trong tương lai
        val nextNote = notes
            .filter { 
                it.reminderDateTime != null && 
                it.enableNotifications && 
                it.reminderDateTime!!.isAfter(now) 
            }
            .minByOrNull { it.reminderDateTime!! }
        
        nextNote?.let { note ->
            val reminder = ScheduledReminder(
                id = "note-${note.id}",
                type = ReminderType.NOTE_REMINDER,
                title = "Nhắc nhở ghi chú",
                message = note.title,
                scheduledTime = note.reminderDateTime!!,
                status = ReminderStatus.SCHEDULED,
                relatedId = note.id
            )
            scheduleReminder(reminder)
        }
    }
    
    override suspend fun findNextReminderTime(
        shift: Shift,
        reminderType: ShiftReminderType,
        fromTime: LocalDateTime
    ): LocalDateTime? {
        val today = fromTime.toLocalDate()
        
        // Tìm ngày làm việc tiếp theo trong vòng 7 ngày
        for (i in 0..6) {
            val checkDate = today.plusDays(i.toLong())
            val dayOfWeek = getDayOfWeekString(checkDate.dayOfWeek)
            
            if (shift.isWorkingDay(checkDate.dayOfWeek)) {
                val reminderTime = calculateReminderTimeForDate(shift, reminderType, checkDate)
                
                // Chỉ trả về nếu thời gian trong tương lai
                if (reminderTime.isAfter(fromTime)) {
                    return reminderTime
                }
            }
        }
        
        return null
    }
    
    override suspend fun onReminderTriggered(reminderId: String, reminderType: ReminderType) {
        // Cập nhật trạng thái nhắc nhở
        scheduledReminders[reminderId]?.let { reminder ->
            scheduledReminders[reminderId] = reminder.copy(
                status = ReminderStatus.TRIGGERED,
                updatedAt = LocalDateTime.now()
            )
        }
        
        // Kích hoạt syncNextReminders để lên lịch nhắc nhở tiếp theo
        syncNextReminders()
    }
    
    override suspend fun onReminderCancelled(reminderId: String, reminderType: ReminderType) {
        // Cập nhật trạng thái nhắc nhở
        scheduledReminders[reminderId]?.let { reminder ->
            scheduledReminders[reminderId] = reminder.copy(
                status = ReminderStatus.CANCELLED,
                updatedAt = LocalDateTime.now()
            )
        }
        
        // Kích hoạt syncNextReminders để lên lịch nhắc nhở tiếp theo
        syncNextReminders()
    }
    
    override suspend fun onActiveShiftChanged(newShiftId: String?) {
        // Kích hoạt syncNextReminders khi đổi ca
        syncNextReminders()
    }
    
    override suspend fun getScheduledReminders(): List<ScheduledReminder> {
        return scheduledReminders.values
            .filter { it.status == ReminderStatus.SCHEDULED }
            .sortedBy { it.scheduledTime }
    }
    
    override suspend fun cancelReminder(reminderId: String) {
        scheduledReminders[reminderId]?.let { reminder ->
            when (reminder.type) {
                ReminderType.SHIFT_DEPARTURE,
                ReminderType.SHIFT_CHECK_IN,
                ReminderType.SHIFT_CHECK_OUT -> {
                    notificationService.cancelShiftReminder(reminderId)
                }
                ReminderType.NOTE_REMINDER -> {
                    notificationService.cancelNoteReminder(reminder.relatedId ?: "")
                }
                else -> {
                    // Handle other types
                }
            }
            
            scheduledReminders[reminderId] = reminder.copy(
                status = ReminderStatus.CANCELLED,
                updatedAt = LocalDateTime.now()
            )
        }
    }
    
    override suspend fun cancelAllReminders() {
        notificationService.cancelAllReminders()
        scheduledReminders.clear()
    }
    
    private suspend fun buildReminderContext(): ReminderContext {
        val settings = settingsRepository.getUserSettings().first()
        val activeShift = settings.activeShiftId?.let { 
            shiftRepository.getShiftById(it) 
        }
        val allShifts = shiftRepository.getAllShifts().first()
        val notes = noteRepository.getActiveNotesFlow().first()
        
        return ReminderContext(
            currentTime = LocalDateTime.now(),
            activeShift = activeShift,
            upcomingShifts = allShifts,
            pendingNotes = notes,
            config = ReminderConfig(
                enableShiftReminders = true,
                enableNoteReminders = true,
                snoozeMinutes = settings.snoozeMinutes,
                maxSnoozeCount = settings.maxSnoozeCount
            )
        )
    }
    
    private suspend fun scheduleReminder(reminder: ScheduledReminder) {
        val timeMillis = reminder.scheduledTime
            .atZone(java.time.ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli()
        
        when (reminder.type) {
            ReminderType.SHIFT_DEPARTURE,
            ReminderType.SHIFT_CHECK_IN,
            ReminderType.SHIFT_CHECK_OUT -> {
                reminder.relatedId?.let { shiftId ->
                    shiftRepository.getShiftById(shiftId)?.let { shift ->
                        notificationService.scheduleShiftReminder(shift, timeMillis)
                    }
                }
            }
            ReminderType.NOTE_REMINDER -> {
                reminder.relatedId?.let { noteId ->
                    noteRepository.getNoteById(noteId)?.let { note ->
                        notificationService.scheduleNoteReminder(note)
                    }
                }
            }
            else -> {
                // Handle other types
            }
        }
        
        scheduledReminders[reminder.id] = reminder
    }
    
    private fun calculateReminderTimeForDate(
        shift: Shift,
        reminderType: ShiftReminderType,
        date: LocalDate
    ): LocalDateTime {
        return when (reminderType) {
            ShiftReminderType.DEPARTURE -> {
                val departureTime = LocalTime.parse(shift.departureTime)
                date.atTime(departureTime).minusMinutes(shift.remindBeforeDeparture.toLong())
            }
            ShiftReminderType.CHECK_IN -> {
                val startTime = LocalTime.parse(shift.startTime)
                date.atTime(startTime).minusMinutes(shift.remindBeforeStart.toLong())
            }
            ShiftReminderType.CHECK_OUT -> {
                val endTime = LocalTime.parse(shift.maxEndTime)
                val endDateTime = if (shift.isNightShift && endTime.isBefore(LocalTime.parse(shift.startTime))) {
                    date.plusDays(1).atTime(endTime)
                } else {
                    date.atTime(endTime)
                }
                endDateTime.plusMinutes(shift.remindAfterEnd.toLong())
            }
        }
    }
    
    private fun getDayOfWeekString(dayOfWeek: DayOfWeek): String {
        return when (dayOfWeek) {
            DayOfWeek.MONDAY -> "Mon"
            DayOfWeek.TUESDAY -> "Tue"
            DayOfWeek.WEDNESDAY -> "Wed"
            DayOfWeek.THURSDAY -> "Thu"
            DayOfWeek.FRIDAY -> "Fri"
            DayOfWeek.SATURDAY -> "Sat"
            DayOfWeek.SUNDAY -> "Sun"
        }
    }
}
